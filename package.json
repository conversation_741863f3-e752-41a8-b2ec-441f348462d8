{"name": "applied2", "version": "0.0.0", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@types/chai": "^5.2.2", "@types/mocha": "^10.0.10", "@types/sinon": "^10.0.16", "chai": "^4.3.3", "diff": "^8.0.2", "mocha": "^10.2.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "vite": "^7.0.1", "vite-plugin-checker": "^0.9.3", "vite-plugin-node-polyfills": "^0.24.0"}, "engines": {"node": "^20.19.0 || >=22.12.0"}}